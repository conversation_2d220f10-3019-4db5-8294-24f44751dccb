import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

import 'package:moon_memory_camera/core/errors/error_handler.dart';
import 'package:moon_memory_camera/core/errors/app_exceptions.dart';

void main() {
  group('ErrorHandler Tests', () {
    group('handleError', () {
      test('should return same AppException when input is AppException', () {
        // Arrange
        const inputError = AuthException('Test error', code: 'test_code');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, equals(inputError));
        expect(result.code, equals('test_code'));
      });

      test('should convert AuthException to AuthException', () {
        // Arrange
        const inputError = supabase.AuthException('Invalid login credentials');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<AuthException>());
        expect(result.code, equals('invalid_credentials'));
      });

      test('should convert SocketException to NetworkException', () {
        // Arrange
        final inputError = const SocketException('No internet connection');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<NetworkException>());
        expect(result.code, equals('no_internet'));
      });

      test('should convert HttpException to NetworkException', () {
        // Arrange
        final inputError = const HttpException('HTTP Error');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<NetworkException>());
        expect(result.code, equals('http_error'));
      });

      test('should convert unknown error to GeneralException', () {
        // Arrange
        final inputError = Exception('Unknown error');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<GeneralException>());
        expect(result.message, contains('Unknown error'));
      });
    });

    group('Auth Error Handling', () {
      test('should handle invalid credentials error', () {
        // Arrange
        const inputError = supabase.AuthException('Invalid login credentials');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<AuthException>());
        expect(result.code, equals('invalid_credentials'));
      });

      test('should handle email not confirmed error', () {
        // Arrange
        const inputError = supabase.AuthException('Email not confirmed');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<AuthException>());
        expect(result.code, equals('email_not_confirmed'));
      });

      test('should handle user not found error', () {
        // Arrange
        const inputError = supabase.AuthException('User not found');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<AuthException>());
        expect(result.code, equals('user_not_found'));
      });

      test('should handle general auth error', () {
        // Arrange
        const inputError = supabase.AuthException('Some other auth error');

        // Act
        final result = ErrorHandler.handleError(inputError);

        // Assert
        expect(result, isA<AuthException>());
        expect(result.code, equals('general_error'));
        expect(result.message, equals('Some other auth error'));
      });
    });
  });
}
