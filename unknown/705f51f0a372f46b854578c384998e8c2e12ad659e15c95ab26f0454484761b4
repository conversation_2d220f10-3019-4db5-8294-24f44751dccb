import 'package:device_info_plus/device_info_plus.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:logger/logger.dart';

class DeviceService {
  final _logger = Logger();
  final _deviceInfo = DeviceInfoPlugin();
  static const _deviceIdKey = 'device_unique_id';

  Future<String> getUniqueDeviceId() async {
    try {
      // محاولة الحصول على معرف الجهاز المحفوظ
      final prefs = await SharedPreferences.getInstance();
      String? savedId = prefs.getString(_deviceIdKey);
      
      if (savedId != null) {
        return savedId;
      }

      // إنشاء معرف جديد إذا لم يكن موجوداً
      final androidInfo = await _deviceInfo.androidInfo;
      final uniqueString = '${androidInfo.id}'
          '_${androidInfo.device}'
          '_${androidInfo.serialNumber}'
          '_${const Uuid().v4()}';

      // إنشاء بصمة فريدة
      final deviceId = _generateHash(uniqueString);
      
      // حفظ المعرف للاستخدام المستقبلي
      await prefs.setString(_deviceIdKey, deviceId);
      
      return deviceId;
    } catch (e) {
      _logger.e('Error generating device ID: $e');
      // حل بديل في حالة الفشل
      return 'fallback_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  String _generateHash(String input) {
    var bytes = utf8.encode(input);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<Map<String, String>> getDeviceInfo() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      final deviceId = await getUniqueDeviceId();
      
      return {
        'device_id': deviceId,
        'device_name': '${androidInfo.brand} ${androidInfo.model}',
        'model': androidInfo.model,
        'brand': androidInfo.brand,
        'product': androidInfo.product,
        'hardware': androidInfo.hardware,
        'android_version': androidInfo.version.release,
      };
    } catch (e) {
      _logger.e('Error getting device info: $e');
      return {
        'device_id': 'error_${DateTime.now().millisecondsSinceEpoch}',
        'device_name': 'Unknown Device',
        'model': 'Unknown',
        'brand': 'Unknown',
      };
    }
  }
}
