import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';

import '../utils/logger.dart';

final permissionsServiceProvider = Provider((ref) => PermissionsService());

class PermissionsService {
  final _logger = getLogger();

  Future<bool> requestAllPermissions() async {
    try {
      // طلب إذن الكاميرا
      final cameraStatus = await Permission.camera.request();
      if (!cameraStatus.isGranted) {
        _logger.w('Camera permission denied');
        return false;
      }

      // طلب إذن الميكروفون
      final microphoneStatus = await Permission.microphone.request();
      if (!microphoneStatus.isGranted) {
        _logger.w('Microphone permission denied');
        return false;
      }

      // طلب إذن الموقع
      final locationStatus = await Permission.location.request();
      if (!locationStatus.isGranted) {
        _logger.w('Location permission denied');
        return false;
      }

      // التحقق من تفعيل خدمة الموقع
      final isLocationEnabled = await Geolocator.isLocationServiceEnabled();
      if (!isLocationEnabled) {
        _logger.w('Location service is disabled');
        return false;
      }

      _logger.i('All permissions granted successfully');
      return true;
    } catch (e) {
      _logger.e('Error requesting permissions', error: e);
      return false;
    }
  }

  Future<bool> checkAllPermissions() async {
    try {
      final cameraStatus = await Permission.camera.status;
      final microphoneStatus = await Permission.microphone.status;
      final locationStatus = await Permission.location.status;
      final isLocationEnabled = await Geolocator.isLocationServiceEnabled();

      return cameraStatus.isGranted &&
          microphoneStatus.isGranted &&
          locationStatus.isGranted &&
          isLocationEnabled;
    } catch (e) {
      _logger.e('Error checking permissions', error: e);
      return false;
    }
  }
}
