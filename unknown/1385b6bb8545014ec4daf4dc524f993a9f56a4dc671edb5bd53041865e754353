import 'package:flutter_test/flutter_test.dart';
import 'package:moon_memory_camera/core/errors/app_exceptions.dart';

void main() {
  group('AuthService Tests', () {
    group('Exception Tests', () {
      test('AuthException should have correct properties', () {
        // Arrange
        const exception = AuthException(
          'Test error message',
          code: 'test_code',
        );

        // Assert
        expect(exception.message, equals('Test error message'));
        expect(exception.code, equals('test_code'));
      });

      test('AuthException should return localized message', () {
        // Arrange
        const exception = AuthException(
          'Invalid credentials',
          code: 'invalid_credentials',
        );

        // Act
        final localizedMessage = exception.getLocalizedMessage();

        // Assert
        expect(localizedMessage, isNotEmpty);
      });
    });

    group('Validation Tests', () {
      test('should validate national ID format', () {
        // Test cases for national ID validation
        const validId = '1234567890';
        const invalidId = '123';

        expect(validId.length, equals(10));
        expect(invalidId.length, lessThan(10));
      });

      test('should validate email format', () {
        // Test email format validation
        const nationalId = '1234567890';
        final email = '$<EMAIL>';

        expect(email, contains('@moon-memory.com'));
        expect(email, startsWith(nationalId));
      });
    });

    group('Error Handling Tests', () {
      test('should handle network errors', () {
        // Test network error handling
        const networkError = NetworkException(
          'No internet connection',
          code: 'no_internet',
        );

        expect(networkError.code, equals('no_internet'));
        expect(networkError.message, contains('internet'));
      });

      test('should handle general errors', () {
        // Test general error handling
        const generalError = GeneralException('Unknown error');

        expect(generalError.message, equals('Unknown error'));
      });
    });
  });
}
