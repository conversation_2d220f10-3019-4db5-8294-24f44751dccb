import 'package:easy_localization/easy_localization.dart';

/// استثناءات التطبيق المخصصة
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppException(this.message, {this.code, this.originalError});

  String getLocalizedMessage() => message;

  @override
  String toString() => 'AppException: $message';
}

/// استثناءات المصادقة
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.originalError});

  @override
  String getLocalizedMessage() {
    switch (code) {
      case 'invalid_credentials':
        return 'auth.errors.invalid_credentials'.tr();
      case 'device_unauthorized':
        return 'auth.errors.device_unauthorized'.tr();
      case 'device_limit_reached':
        return 'auth.errors.device_limit_reached'.tr();
      case 'user_not_found':
        return 'auth.errors.user_not_found'.tr();
      case 'session_expired':
        return 'auth.errors.session_expired'.tr();
      default:
        return 'auth.errors.general_error'.tr();
    }
  }
}

/// استثناءات الشبكة
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.originalError});

  @override
  String getLocalizedMessage() {
    switch (code) {
      case 'no_internet':
        return 'errors.no_internet'.tr();
      case 'timeout':
        return 'errors.timeout'.tr();
      case 'server_error':
        return 'errors.server'.tr();
      default:
        return 'errors.network'.tr();
    }
  }
}

/// استثناءات الكاميرا
class CameraException extends AppException {
  const CameraException(super.message, {super.code, super.originalError});

  @override
  String getLocalizedMessage() {
    switch (code) {
      case 'camera_not_available':
        return 'camera.errors.camera_not_available'.tr();
      case 'permission_denied':
        return 'camera.errors.permission_denied'.tr();
      case 'photo_save_failed':
        return 'camera.errors.photo_save_failed'.tr();
      case 'video_record_failed':
        return 'camera.errors.video_record_failed'.tr();
      default:
        return 'camera.errors.general'.tr();
    }
  }
}

/// استثناءات الموقع
class LocationException extends AppException {
  const LocationException(super.message, {super.code, super.originalError});

  @override
  String getLocalizedMessage() {
    switch (code) {
      case 'permission_denied':
        return 'location.errors.permission_denied'.tr();
      case 'service_disabled':
        return 'location.errors.service_disabled'.tr();
      case 'location_not_found':
        return 'location.errors.location_not_found'.tr();
      default:
        return 'location.errors.general'.tr();
    }
  }
}

/// استثناءات التخزين
class StorageException extends AppException {
  const StorageException(super.message, {super.code, super.originalError});

  @override
  String getLocalizedMessage() {
    switch (code) {
      case 'upload_failed':
        return 'storage.errors.upload_failed'.tr();
      case 'download_failed':
        return 'storage.errors.download_failed'.tr();
      case 'file_not_found':
        return 'storage.errors.file_not_found'.tr();
      case 'insufficient_space':
        return 'storage.errors.insufficient_space'.tr();
      default:
        return 'storage.errors.general'.tr();
    }
  }
}

/// استثناءات عامة
class GeneralException extends AppException {
  const GeneralException(super.message, {super.code, super.originalError});

  @override
  String getLocalizedMessage() {
    return message.isNotEmpty ? message : 'errors.general'.tr();
  }
}
