include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.mocks.dart"
    - "**/generated_plugin_registrant.dart"

linter:
  rules:
    # تفعيل القواعد المهمة
    avoid_print: true
    prefer_single_quotes: true
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    avoid_unnecessary_containers: true
    use_key_in_widget_constructors: true
    sized_box_for_whitespace: true
    sort_child_properties_last: true

    # تعطيل بعض القواعد الصارمة
    lines_longer_than_80_chars: false
    always_specify_types: false
    prefer_expression_function_bodies: false
